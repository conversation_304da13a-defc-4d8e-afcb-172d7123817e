<?php
$content = ob_get_clean();
ob_start();
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-chart-bar me-2 text-primary"></i>
                Reports & Analytics
            </h1>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-download me-1"></i>
                    Quick Export
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="<?= url('/reports/domain-expiry?format=csv') ?>">
                        <i class="fas fa-globe me-1"></i> Domain Expiry Report
                    </a></li>
                    <li><a class="dropdown-item" href="<?= url('/reports/server-expiry?format=csv') ?>">
                        <i class="fas fa-server me-1"></i> Server Expiry Report
                    </a></li>
                    <li><a class="dropdown-item" href="<?= url('/reports/client-summary?format=csv') ?>">
                        <i class="fas fa-users me-1"></i> Client Summary Report
                    </a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Overview -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Clients
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= number_format($stats['clients']['total_clients'] ?? 0) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Active Domains
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= number_format($stats['domains']['active_domains'] ?? 0) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-globe fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Active Servers
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= number_format($stats['servers']['active_servers'] ?? 0) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-server fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Total Mediators
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= number_format($stats['mediators']['total_mediators'] ?? 0) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-handshake fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Report Categories -->
<div class="row">
    <!-- Expiry Reports -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Expiry Reports
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="<?= url('/reports/domain-expiry') ?>" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">
                                <i class="fas fa-globe text-success me-2"></i>
                                Domain Expiry Report
                            </h6>
                            <small class="text-muted">View</small>
                        </div>
                        <p class="mb-1">Track domains expiring in the next 30 days and already expired domains.</p>
                        <small class="text-muted">Includes renewal costs and client information.</small>
                    </a>
                    
                    <a href="<?= url('/reports/server-expiry') ?>" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">
                                <i class="fas fa-server text-info me-2"></i>
                                Server Expiry Report
                            </h6>
                            <small class="text-muted">View</small>
                        </div>
                        <p class="mb-1">Monitor servers expiring in the next 30 days and already expired servers.</p>
                        <small class="text-muted">Includes server specifications and renewal costs.</small>
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Financial Reports -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-success">
                    <i class="fas fa-dollar-sign me-2"></i>
                    Financial Reports
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="<?= url('/reports/revenue') ?>" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">
                                <i class="fas fa-chart-line text-success me-2"></i>
                                Revenue Analysis
                            </h6>
                            <small class="text-muted">View</small>
                        </div>
                        <p class="mb-1">Comprehensive revenue analysis by time period, client, and service type.</p>
                        <small class="text-muted">Includes domain and server revenue breakdown.</small>
                    </a>
                    
                    <a href="<?= url('/reports/client-summary') ?>" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">
                                <i class="fas fa-users text-primary me-2"></i>
                                Client Summary Report
                            </h6>
                            <small class="text-muted">View</small>
                        </div>
                        <p class="mb-1">Detailed summary of all clients with their domains, servers, and total value.</p>
                        <small class="text-muted">Perfect for client relationship management.</small>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Calendar Reports -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-calendar-alt me-2"></i>
                    Calendar Reports
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="<?= url('/reports/renewal-calendar') ?>" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">
                                <i class="fas fa-calendar-check text-info me-2"></i>
                                Renewal Calendar
                            </h6>
                            <small class="text-muted">View</small>
                        </div>
                        <p class="mb-1">Monthly calendar view of all upcoming domain and server renewals.</p>
                        <small class="text-muted">Plan ahead and never miss a renewal.</small>
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Custom Reports -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-warning">
                    <i class="fas fa-cogs me-2"></i>
                    Custom Reports
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center py-4">
                    <i class="fas fa-tools fa-3x text-muted mb-3"></i>
                    <h6 class="text-muted">Custom Report Builder</h6>
                    <p class="text-muted mb-3">
                        Create custom reports with specific date ranges, filters, and export options.
                    </p>
                    <button class="btn btn-outline-warning" onclick="showCustomReportModal()">
                        <i class="fas fa-plus me-1"></i>
                        Build Custom Report
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="<?= url('/reports/domain-expiry?days=7') ?>" class="btn btn-outline-danger w-100">
                            <i class="fas fa-globe me-1"></i>
                            Domains Expiring This Week
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?= url('/reports/server-expiry?days=7') ?>" class="btn btn-outline-warning w-100">
                            <i class="fas fa-server me-1"></i>
                            Servers Expiring This Week
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?= url('/reports/revenue?start_date=' . date('Y-m-01') . '&end_date=' . date('Y-m-t')) ?>" class="btn btn-outline-success w-100">
                            <i class="fas fa-chart-line me-1"></i>
                            This Month's Revenue
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?= url('/reports/renewal-calendar?month=' . date('m') . '&year=' . date('Y')) ?>" class="btn btn-outline-info w-100">
                            <i class="fas fa-calendar-alt me-1"></i>
                            This Month's Renewals
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom Report Modal -->
<div class="modal fade" id="customReportModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-cogs me-2"></i>
                    Custom Report Builder
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="customReportForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="reportType" class="form-label">Report Type</label>
                                <select class="form-select" id="reportType" name="report_type">
                                    <option value="domain-expiry">Domain Expiry</option>
                                    <option value="server-expiry">Server Expiry</option>
                                    <option value="revenue">Revenue Analysis</option>
                                    <option value="client-summary">Client Summary</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="exportFormat" class="form-label">Export Format</label>
                                <select class="form-select" id="exportFormat" name="format">
                                    <option value="html">View in Browser</option>
                                    <option value="csv">Download CSV</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row" id="dateRangeFields" style="display: none;">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="startDate" class="form-label">Start Date</label>
                                <input type="date" class="form-control" id="startDate" name="start_date" value="<?= date('Y-01-01') ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="endDate" class="form-label">End Date</label>
                                <input type="date" class="form-control" id="endDate" name="end_date" value="<?= date('Y-12-31') ?>">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row" id="daysField">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="days" class="form-label">Days Ahead</label>
                                <select class="form-select" id="days" name="days">
                                    <option value="7">7 days</option>
                                    <option value="15">15 days</option>
                                    <option value="30" selected>30 days</option>
                                    <option value="60">60 days</option>
                                    <option value="90">90 days</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="generateCustomReport()">
                    <i class="fas fa-chart-bar me-1"></i>
                    Generate Report
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function showCustomReportModal() {
    new bootstrap.Modal(document.getElementById('customReportModal')).show();
}

function generateCustomReport() {
    const form = document.getElementById('customReportForm');
    const formData = new FormData(form);
    const params = new URLSearchParams(formData);
    const reportType = formData.get('report_type');
    
    const url = `<?= url('/reports/') ?>${reportType}?${params.toString()}`;
    window.open(url, '_blank');
    
    bootstrap.Modal.getInstance(document.getElementById('customReportModal')).hide();
}

$(document).ready(function() {
    // Show/hide date range fields based on report type
    $('#reportType').on('change', function() {
        const reportType = $(this).val();
        if (reportType === 'revenue') {
            $('#dateRangeFields').show();
            $('#daysField').hide();
        } else {
            $('#dateRangeFields').hide();
            $('#daysField').show();
        }
    });
});
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_PATH . '/views/layouts/app.php';
?>
