<?php
$content = ob_get_clean();
ob_start();
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-handshake me-2 text-primary"></i>
                Add New Mediator
            </h1>
            <a href="<?= url('/mediators') ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                Back to Mediators
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle me-1"></i>
                    Mediator Information
                </h6>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= url('/mediators') ?>" id="mediatorForm">
                    <?= csrf_field() ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    Full Name <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control <?= has_error('name') ? 'is-invalid' : '' ?>" 
                                       id="name" 
                                       name="name" 
                                       value="<?= e(old('name')) ?>"
                                       placeholder="Enter mediator's full name"
                                       required>
                                <?php if (has_error('name')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('name')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="company" class="form-label">Company</label>
                                <input type="text" 
                                       class="form-control <?= has_error('company') ? 'is-invalid' : '' ?>" 
                                       id="company" 
                                       name="company" 
                                       value="<?= e(old('company')) ?>"
                                       placeholder="Company name (optional)">
                                <?php if (has_error('company')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('company')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" 
                                       class="form-control <?= has_error('email') ? 'is-invalid' : '' ?>" 
                                       id="email" 
                                       name="email" 
                                       value="<?= e(old('email')) ?>"
                                       placeholder="<EMAIL>">
                                <?php if (has_error('email')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('email')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" 
                                       class="form-control <?= has_error('phone') ? 'is-invalid' : '' ?>" 
                                       id="phone" 
                                       name="phone" 
                                       value="<?= e(old('phone')) ?>"
                                       placeholder="+****************">
                                <?php if (has_error('phone')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('phone')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">Address</label>
                        <textarea class="form-control <?= has_error('address') ? 'is-invalid' : '' ?>" 
                                  id="address" 
                                  name="address" 
                                  rows="3"
                                  placeholder="Enter full address"><?= e(old('address')) ?></textarea>
                        <?php if (has_error('address')): ?>
                            <div class="invalid-feedback">
                                <?= e(error('address')) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="commission_rate" class="form-label">Commission Rate (%)</label>
                                <div class="input-group">
                                    <input type="number" 
                                           class="form-control <?= has_error('commission_rate') ? 'is-invalid' : '' ?>" 
                                           id="commission_rate" 
                                           name="commission_rate" 
                                           value="<?= e(old('commission_rate', '5.0')) ?>"
                                           step="0.1"
                                           min="0"
                                           max="100"
                                           placeholder="5.0">
                                    <span class="input-group-text">%</span>
                                    <?php if (has_error('commission_rate')): ?>
                                        <div class="invalid-feedback">
                                            <?= e(error('commission_rate')) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="form-text">
                                    Percentage commission for transactions
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control <?= has_error('notes') ? 'is-invalid' : '' ?>" 
                                  id="notes" 
                                  name="notes" 
                                  rows="4"
                                  placeholder="Additional notes about the mediator"><?= e(old('notes')) ?></textarea>
                        <?php if (has_error('notes')): ?>
                            <div class="invalid-feedback">
                                <?= e(error('notes')) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="<?= url('/mediators') ?>" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            Create Mediator
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-lightbulb me-1"></i>
                    Tips
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-1"></i> Mediator Information</h6>
                    <ul class="mb-0 small">
                        <li>Only the mediator name is required</li>
                        <li>Commission rate is used for calculating earnings</li>
                        <li>Email and phone help with communication</li>
                        <li>Company field is useful for business mediators</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-1"></i> Commission Tracking</h6>
                    <p class="mb-0 small">
                        The system will automatically calculate commissions based on 
                        the rate you set when domains or servers are assigned to this mediator.
                    </p>
                </div>
            </div>
        </div>
        
        <div class="card shadow mt-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-success">
                    <i class="fas fa-chart-line me-1"></i>
                    Commission Examples
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Sale Amount</th>
                                <th>5% Commission</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>$100</td>
                                <td class="text-success">$5.00</td>
                            </tr>
                            <tr>
                                <td>$500</td>
                                <td class="text-success">$25.00</td>
                            </tr>
                            <tr>
                                <td>$1,000</td>
                                <td class="text-success">$50.00</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Form validation
    $('#mediatorForm').on('submit', function(e) {
        let isValid = true;
        
        // Check required fields
        const name = $('#name').val().trim();
        if (!name) {
            $('#name').addClass('is-invalid');
            isValid = false;
        } else {
            $('#name').removeClass('is-invalid');
        }
        
        // Validate email if provided
        const email = $('#email').val().trim();
        if (email && !isValidEmail(email)) {
            $('#email').addClass('is-invalid');
            isValid = false;
        } else {
            $('#email').removeClass('is-invalid');
        }
        
        // Validate commission rate
        const commissionRate = parseFloat($('#commission_rate').val());
        if (isNaN(commissionRate) || commissionRate < 0 || commissionRate > 100) {
            $('#commission_rate').addClass('is-invalid');
            isValid = false;
        } else {
            $('#commission_rate').removeClass('is-invalid');
        }
        
        if (!isValid) {
            e.preventDefault();
            ClientManager.showAlert('Please correct the errors below.', 'error');
        }
    });
    
    // Real-time validation
    $('#email').on('blur', function() {
        const email = $(this).val().trim();
        if (email && !isValidEmail(email)) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    
    $('#name').on('input', function() {
        if ($(this).val().trim()) {
            $(this).removeClass('is-invalid');
        }
    });
    
    $('#commission_rate').on('input', function() {
        const rate = parseFloat($(this).val());
        if (isNaN(rate) || rate < 0 || rate > 100) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
});

function isValidEmail(email) {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
}
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_PATH . '/views/layouts/app.php';
?>
