<?php
$sql = "CREATE TABLE IF NOT EXISTS domains (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_id INT NOT NULL,
    mediator_id INT,
    domain_name VARCHAR(255) NOT NULL,
    registrar VARCHAR(100),
    purchase_date DATE NOT NULL,
    expiry_date DATE NOT NULL,
    renewal_cost DECIMAL(10,2),
    auto_renewal BOOLEAN DEFAULT FALSE,
    nameservers TEXT,
    status ENUM('active', 'expired', 'suspended', 'transferred') DEFAULT 'active',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY (mediator_id) REFERENCES mediators(id) ON DELETE SET NULL
)";

$this->connection->exec($sql);
