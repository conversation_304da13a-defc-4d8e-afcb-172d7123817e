<?php
$content = ob_get_clean();
ob_start();
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-globe me-2 text-primary"></i>
                Edit Domain - <?= e($domain['domain_name']) ?>
            </h1>
            <a href="<?= url('/domains/' . $domain['id']) ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                Back to Domain
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle me-1"></i>
                    Domain Information
                </h6>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= url('/domains/' . $domain['id']) ?>" id="domainForm">
                    <?= csrf_field() ?>
                    <?= method_field('PUT') ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="client_id" class="form-label">
                                    Client <span class="text-danger">*</span>
                                </label>
                                <select class="form-select <?= has_error('client_id') ? 'is-invalid' : '' ?>" 
                                        id="client_id" 
                                        name="client_id" 
                                        required>
                                    <option value="">Select a client</option>
                                    <?php foreach ($clients as $client): ?>
                                        <option value="<?= $client['id'] ?>" <?= old('client_id', $domain['client_id']) == $client['id'] ? 'selected' : '' ?>>
                                            <?= e($client['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (has_error('client_id')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('client_id')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="mediator_id" class="form-label">Mediator</label>
                                <select class="form-select <?= has_error('mediator_id') ? 'is-invalid' : '' ?>" 
                                        id="mediator_id" 
                                        name="mediator_id">
                                    <option value="">No mediator</option>
                                    <?php foreach ($mediators as $mediator): ?>
                                        <option value="<?= $mediator['id'] ?>" <?= old('mediator_id', $domain['mediator_id']) == $mediator['id'] ? 'selected' : '' ?>>
                                            <?= e($mediator['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (has_error('mediator_id')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('mediator_id')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="domain_name" class="form-label">
                                    Domain Name <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control <?= has_error('domain_name') ? 'is-invalid' : '' ?>" 
                                       id="domain_name" 
                                       name="domain_name" 
                                       value="<?= e(old('domain_name', $domain['domain_name'])) ?>"
                                       placeholder="example.com"
                                       required>
                                <?php if (has_error('domain_name')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('domain_name')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="registrar" class="form-label">Registrar</label>
                                <input type="text" 
                                       class="form-control <?= has_error('registrar') ? 'is-invalid' : '' ?>" 
                                       id="registrar" 
                                       name="registrar" 
                                       value="<?= e(old('registrar', $domain['registrar'])) ?>"
                                       placeholder="GoDaddy, Namecheap, etc.">
                                <?php if (has_error('registrar')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('registrar')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="purchase_date" class="form-label">
                                    Purchase Date <span class="text-danger">*</span>
                                </label>
                                <input type="date" 
                                       class="form-control <?= has_error('purchase_date') ? 'is-invalid' : '' ?>" 
                                       id="purchase_date" 
                                       name="purchase_date" 
                                       value="<?= e(old('purchase_date', $domain['purchase_date'])) ?>"
                                       required>
                                <?php if (has_error('purchase_date')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('purchase_date')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="expiry_date" class="form-label">
                                    Expiry Date <span class="text-danger">*</span>
                                </label>
                                <input type="date" 
                                       class="form-control <?= has_error('expiry_date') ? 'is-invalid' : '' ?>" 
                                       id="expiry_date" 
                                       name="expiry_date" 
                                       value="<?= e(old('expiry_date', $domain['expiry_date'])) ?>"
                                       required>
                                <?php if (has_error('expiry_date')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('expiry_date')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="renewal_cost" class="form-label">Renewal Cost</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" 
                                           class="form-control <?= has_error('renewal_cost') ? 'is-invalid' : '' ?>" 
                                           id="renewal_cost" 
                                           name="renewal_cost" 
                                           value="<?= e(old('renewal_cost', $domain['renewal_cost'])) ?>"
                                           step="0.01"
                                           min="0"
                                           placeholder="0.00">
                                    <?php if (has_error('renewal_cost')): ?>
                                        <div class="invalid-feedback">
                                            <?= e(error('renewal_cost')) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select <?= has_error('status') ? 'is-invalid' : '' ?>" 
                                        id="status" 
                                        name="status">
                                    <option value="active" <?= old('status', $domain['status']) == 'active' ? 'selected' : '' ?>>Active</option>
                                    <option value="expired" <?= old('status', $domain['status']) == 'expired' ? 'selected' : '' ?>>Expired</option>
                                    <option value="suspended" <?= old('status', $domain['status']) == 'suspended' ? 'selected' : '' ?>>Suspended</option>
                                    <option value="pending" <?= old('status', $domain['status']) == 'pending' ? 'selected' : '' ?>>Pending</option>
                                </select>
                                <?php if (has_error('status')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('status')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" 
                                   type="checkbox" 
                                   id="auto_renewal" 
                                   name="auto_renewal" 
                                   value="1"
                                   <?= old('auto_renewal', $domain['auto_renewal']) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="auto_renewal">
                                Enable Auto Renewal
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="nameservers" class="form-label">Nameservers</label>
                        <textarea class="form-control <?= has_error('nameservers') ? 'is-invalid' : '' ?>" 
                                  id="nameservers" 
                                  name="nameservers" 
                                  rows="3"
                                  placeholder="ns1.example.com&#10;ns2.example.com"><?= e(old('nameservers', $domain['nameservers'])) ?></textarea>
                        <?php if (has_error('nameservers')): ?>
                            <div class="invalid-feedback">
                                <?= e(error('nameservers')) ?>
                            </div>
                        <?php endif; ?>
                        <div class="form-text">Enter one nameserver per line</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control <?= has_error('notes') ? 'is-invalid' : '' ?>" 
                                  id="notes" 
                                  name="notes" 
                                  rows="4"
                                  placeholder="Additional notes about the domain"><?= e(old('notes', $domain['notes'])) ?></textarea>
                        <?php if (has_error('notes')): ?>
                            <div class="invalid-feedback">
                                <?= e(error('notes')) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="<?= url('/domains/' . $domain['id']) ?>" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            Update Domain
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-lightbulb me-1"></i>
                    Tips
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-1"></i> Editing Domain</h6>
                    <ul class="mb-0 small">
                        <li>Domain name should be without http:// or www</li>
                        <li>Purchase and expiry dates help track renewals</li>
                        <li>Auto renewal prevents accidental expiration</li>
                        <li>Status affects how the domain appears in lists</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-1"></i> Expiry Alerts</h6>
                    <p class="mb-0 small">
                        The system will automatically send alerts 30, 15, 7, and 1 days 
                        before the domain expires.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Domain name validation
    $('#domain_name').on('blur', function() {
        let domain = $(this).val().trim().toLowerCase();
        
        // Remove protocol and www
        domain = domain.replace(/^https?:\/\//, '');
        domain = domain.replace(/^www\./, '');
        domain = domain.replace(/\/$/, '');
        
        $(this).val(domain);
        
        // Basic domain validation
        const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
        if (domain && !domainRegex.test(domain)) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    
    // Form validation
    $('#domainForm').on('submit', function(e) {
        let isValid = true;
        
        // Check required fields
        const requiredFields = ['client_id', 'domain_name', 'purchase_date', 'expiry_date'];
        requiredFields.forEach(function(field) {
            const value = $(`#${field}`).val().trim();
            if (!value) {
                $(`#${field}`).addClass('is-invalid');
                isValid = false;
            } else {
                $(`#${field}`).removeClass('is-invalid');
            }
        });
        
        // Validate dates
        const purchaseDate = new Date($('#purchase_date').val());
        const expiryDate = new Date($('#expiry_date').val());
        
        if (expiryDate <= purchaseDate) {
            $('#expiry_date').addClass('is-invalid');
            isValid = false;
            ClientManager.showAlert('Expiry date must be after purchase date.', 'error');
        }
        
        if (!isValid) {
            e.preventDefault();
            ClientManager.showAlert('Please correct the errors below.', 'error');
        }
    });
});
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_PATH . '/views/layouts/app.php';
?>
