<?php
/**
 * Router Class
 * Handles URL routing and middleware
 */

class Router
{
    private $routes = [];
    private $middleware = [];
    private $currentGroup = [];
    
    public function get($uri, $action)
    {
        $this->addRoute('GET', $uri, $action);
    }
    
    public function post($uri, $action)
    {
        $this->addRoute('POST', $uri, $action);
    }
    
    public function put($uri, $action)
    {
        $this->addRoute('PUT', $uri, $action);
    }
    
    public function delete($uri, $action)
    {
        $this->addRoute('DELETE', $uri, $action);
    }
    
    public function group($attributes, $callback)
    {
        $previousGroup = $this->currentGroup;
        $this->currentGroup = array_merge($this->currentGroup, $attributes);
        
        $callback($this);
        
        $this->currentGroup = $previousGroup;
    }
    
    private function addRoute($method, $uri, $action)
    {
        // Apply group prefix
        if (isset($this->currentGroup['prefix'])) {
            $uri = '/' . trim($this->currentGroup['prefix'], '/') . '/' . trim($uri, '/');
            $uri = rtrim($uri, '/') ?: '/';
        }
        
        // Apply group middleware
        $middleware = [];
        if (isset($this->currentGroup['middleware'])) {
            $middleware = is_array($this->currentGroup['middleware']) 
                ? $this->currentGroup['middleware'] 
                : [$this->currentGroup['middleware']];
        }
        
        $this->routes[] = [
            'method' => $method,
            'uri' => $uri,
            'action' => $action,
            'middleware' => $middleware
        ];
    }
    
    public function dispatch()
    {
        $requestMethod = $_SERVER['REQUEST_METHOD'];
        $requestUri = $this->getRequestUri();
        
        // Handle method override for forms
        if ($requestMethod === 'POST' && isset($_POST['_method'])) {
            $requestMethod = strtoupper($_POST['_method']);
        }
        
        foreach ($this->routes as $route) {
            if ($this->matchRoute($route, $requestMethod, $requestUri)) {
                // Extract parameters
                $params = $this->extractParams($route['uri'], $requestUri);
                
                // Run middleware
                foreach ($route['middleware'] as $middleware) {
                    $this->runMiddleware($middleware);
                }
                
                // Execute action
                $this->executeAction($route['action'], $params);
                return;
            }
        }
        
        // No route found
        $this->handleNotFound();
    }
    
    private function getRequestUri()
    {
        $uri = $_SERVER['REQUEST_URI'];

        // Remove query string
        if (($pos = strpos($uri, '?')) !== false) {
            $uri = substr($uri, 0, $pos);
        }

        // Remove base path for subdirectory installations
        $scriptName = $_SERVER['SCRIPT_NAME'];
        $basePath = dirname($scriptName);

        // Handle subdirectory installations like /projects/nicetech/
        if ($basePath !== '/' && strpos($uri, $basePath) === 0) {
            $uri = substr($uri, strlen($basePath));
        }

        // If accessing index.php directly, remove it from the URI
        if (strpos($uri, '/index.php') === 0) {
            $uri = substr($uri, 10); // Remove '/index.php'
        }

        return $uri ?: '/';
    }
    
    private function matchRoute($route, $method, $uri)
    {
        if ($route['method'] !== $method) {
            return false;
        }
        
        $pattern = $this->convertToRegex($route['uri']);
        return preg_match($pattern, $uri);
    }
    
    private function convertToRegex($uri)
    {
        // Convert {param} to regex capture groups
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $uri);
        return '#^' . $pattern . '$#';
    }
    
    private function extractParams($routeUri, $requestUri)
    {
        $params = [];
        
        // Extract parameter names from route
        preg_match_all('/\{([^}]+)\}/', $routeUri, $paramNames);
        
        // Extract values from request URI
        $pattern = $this->convertToRegex($routeUri);
        preg_match($pattern, $requestUri, $values);
        
        // Combine names and values
        for ($i = 0; $i < count($paramNames[1]); $i++) {
            $params[$paramNames[1][$i]] = $values[$i + 1] ?? null;
        }
        
        return $params;
    }
    
    private function runMiddleware($middleware)
    {
        switch ($middleware) {
            case 'auth':
                if (!Auth::check()) {
                    $this->redirect('/login');
                }
                break;
                
            case 'admin':
                if (!Auth::check() || !Auth::user()->isAdmin()) {
                    $this->handleUnauthorized();
                }
                break;
                
            case 'guest':
                if (Auth::check()) {
                    $this->redirect('/dashboard');
                }
                break;
        }
    }
    
    private function executeAction($action, $params = [])
    {
        if (is_string($action) && strpos($action, '@') !== false) {
            list($controller, $method) = explode('@', $action);
            
            if (!class_exists($controller)) {
                throw new Exception("Controller {$controller} not found");
            }
            
            $controllerInstance = new $controller();
            
            if (!method_exists($controllerInstance, $method)) {
                throw new Exception("Method {$method} not found in {$controller}");
            }
            
            // Pass parameters to the method
            call_user_func_array([$controllerInstance, $method], $params);
            
        } elseif (is_callable($action)) {
            call_user_func_array($action, $params);
        } else {
            throw new Exception("Invalid action");
        }
    }
    
    private function handleNotFound()
    {
        http_response_code(404);
        if (file_exists(APP_PATH . '/views/errors/404.php')) {
            include APP_PATH . '/views/errors/404.php';
        } else {
            echo "404 - Page Not Found";
        }
    }
    
    private function handleUnauthorized()
    {
        http_response_code(403);
        if (file_exists(APP_PATH . '/views/errors/403.php')) {
            include APP_PATH . '/views/errors/403.php';
        } else {
            echo "403 - Unauthorized";
        }
    }
    
    private function redirect($url)
    {
        $config = include CONFIG_PATH . '/config.php';
        $baseUrl = $config['app']['url'];
        
        if (strpos($url, 'http') !== 0) {
            $url = rtrim($baseUrl, '/') . '/' . ltrim($url, '/');
        }
        
        header("Location: $url");
        exit;
    }
}
