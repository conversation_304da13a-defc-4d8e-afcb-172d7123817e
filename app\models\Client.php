<?php
/**
 * Client Model
 */

class Client extends Model
{
    protected $table = 'clients';
    protected $fillable = [
        'name', 'email', 'phone', 'address', 'company', 'notes', 'is_active'
    ];
    
    /**
     * Get client with domains and servers
     */
    public function getClientWithServices($id)
    {
        $client = $this->find($id);
        
        if (!$client) {
            return null;
        }
        
        // Get domains
        $domainModel = new Domain();
        $client['domains'] = $domainModel->where(['client_id' => $id], 'domain_name ASC');
        
        // Get servers
        $serverModel = new Server();
        $client['servers'] = $serverModel->where(['client_id' => $id], 'server_name ASC');
        
        return $client;
    }
    
    /**
     * Get active clients
     */
    public function getActiveClients()
    {
        return $this->where(['is_active' => 1], 'name ASC');
    }
    
    /**
     * Search clients
     */
    public function searchClients($query)
    {
        $sql = "SELECT * FROM {$this->table} 
                WHERE (name LIKE ? OR email LIKE ? OR phone LIKE ? OR company LIKE ?) 
                AND is_active = 1 
                ORDER BY name ASC";
        
        $searchTerm = "%{$query}%";
        return $this->query($sql, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
    }
    
    /**
     * Get client statistics
     */
    public function getStats()
    {
        $domainModel = new Domain();
        $serverModel = new Server();
        
        return [
            'total_clients' => $this->count(['is_active' => 1]),
            'total_domains' => $domainModel->count(),
            'total_servers' => $serverModel->count(),
            'active_domains' => $domainModel->count(['status' => 'active']),
            'active_servers' => $serverModel->count(['status' => 'active'])
        ];
    }
    
    /**
     * Get clients with expiring services
     */
    public function getClientsWithExpiringServices($days = 30)
    {
        $date = date('Y-m-d', strtotime("+{$days} days"));
        
        $sql = "SELECT DISTINCT c.* FROM clients c
                LEFT JOIN domains d ON c.id = d.client_id
                LEFT JOIN servers s ON c.id = s.client_id
                WHERE (d.expiry_date <= ? AND d.status = 'active')
                   OR (s.expiry_date <= ? AND s.status = 'active')
                ORDER BY c.name ASC";
        
        return $this->query($sql, [$date, $date]);
    }
    
    /**
     * Get client revenue summary
     */
    public function getRevenueSummary($clientId)
    {
        $domainModel = new Domain();
        $serverModel = new Server();
        
        // Get domain costs
        $domainCosts = $domainModel->query(
            "SELECT SUM(renewal_cost) as total FROM domains WHERE client_id = ? AND status = 'active'",
            [$clientId]
        );
        
        // Get server costs
        $serverCosts = $serverModel->query(
            "SELECT SUM(renewal_cost) as total FROM servers WHERE client_id = ? AND status = 'active'",
            [$clientId]
        );
        
        return [
            'domain_costs' => $domainCosts[0]['total'] ?? 0,
            'server_costs' => $serverCosts[0]['total'] ?? 0,
            'total_costs' => ($domainCosts[0]['total'] ?? 0) + ($serverCosts[0]['total'] ?? 0)
        ];
    }
}
