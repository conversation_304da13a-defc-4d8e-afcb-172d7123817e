<?php
$content = ob_get_clean();
ob_start();
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-server me-2 text-primary"></i>
                Add New Server
            </h1>
            <a href="<?= url('/servers') ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                Back to Servers
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle me-1"></i>
                    Server Information
                </h6>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= url('/servers') ?>" id="serverForm">
                    <?= csrf_field() ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="client_id" class="form-label">
                                    Client <span class="text-danger">*</span>
                                </label>
                                <select class="form-select <?= has_error('client_id') ? 'is-invalid' : '' ?>" 
                                        id="client_id" 
                                        name="client_id" 
                                        required>
                                    <option value="">Select a client</option>
                                    <?php foreach ($clients as $client): ?>
                                        <option value="<?= $client['id'] ?>" <?= old('client_id') == $client['id'] ? 'selected' : '' ?>>
                                            <?= e($client['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (has_error('client_id')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('client_id')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="mediator_id" class="form-label">Mediator</label>
                                <select class="form-select <?= has_error('mediator_id') ? 'is-invalid' : '' ?>" 
                                        id="mediator_id" 
                                        name="mediator_id">
                                    <option value="">No mediator</option>
                                    <?php foreach ($mediators as $mediator): ?>
                                        <option value="<?= $mediator['id'] ?>" <?= old('mediator_id') == $mediator['id'] ? 'selected' : '' ?>>
                                            <?= e($mediator['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (has_error('mediator_id')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('mediator_id')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="server_name" class="form-label">
                                    Server Name <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control <?= has_error('server_name') ? 'is-invalid' : '' ?>" 
                                       id="server_name" 
                                       name="server_name" 
                                       value="<?= e(old('server_name')) ?>"
                                       placeholder="Web Server 1, Mail Server, etc."
                                       required>
                                <?php if (has_error('server_name')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('server_name')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="provider" class="form-label">Provider</label>
                                <input type="text" 
                                       class="form-control <?= has_error('provider') ? 'is-invalid' : '' ?>" 
                                       id="provider" 
                                       name="provider" 
                                       value="<?= e(old('provider')) ?>"
                                       placeholder="AWS, DigitalOcean, etc.">
                                <?php if (has_error('provider')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('provider')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="server_type" class="form-label">
                                    Server Type <span class="text-danger">*</span>
                                </label>
                                <select class="form-select <?= has_error('server_type') ? 'is-invalid' : '' ?>" 
                                        id="server_type" 
                                        name="server_type" 
                                        required>
                                    <option value="">Select server type</option>
                                    <?php foreach ($server_types as $type): ?>
                                        <option value="<?= $type ?>" <?= old('server_type') == $type ? 'selected' : '' ?>>
                                            <?= ucfirst($type) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (has_error('server_type')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('server_type')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ip_address" class="form-label">IP Address</label>
                                <input type="text" 
                                       class="form-control <?= has_error('ip_address') ? 'is-invalid' : '' ?>" 
                                       id="ip_address" 
                                       name="ip_address" 
                                       value="<?= e(old('ip_address')) ?>"
                                       placeholder="***********">
                                <?php if (has_error('ip_address')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('ip_address')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="purchase_date" class="form-label">
                                    Purchase Date <span class="text-danger">*</span>
                                </label>
                                <input type="date" 
                                       class="form-control <?= has_error('purchase_date') ? 'is-invalid' : '' ?>" 
                                       id="purchase_date" 
                                       name="purchase_date" 
                                       value="<?= e(old('purchase_date', date('Y-m-d'))) ?>"
                                       required>
                                <?php if (has_error('purchase_date')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('purchase_date')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="expiry_date" class="form-label">
                                    Expiry Date <span class="text-danger">*</span>
                                </label>
                                <input type="date" 
                                       class="form-control <?= has_error('expiry_date') ? 'is-invalid' : '' ?>" 
                                       id="expiry_date" 
                                       name="expiry_date" 
                                       value="<?= e(old('expiry_date')) ?>"
                                       required>
                                <?php if (has_error('expiry_date')): ?>
                                    <div class="invalid-feedback">
                                        <?= e(error('expiry_date')) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="renewal_cost" class="form-label">Renewal Cost</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" 
                                           class="form-control <?= has_error('renewal_cost') ? 'is-invalid' : '' ?>" 
                                           id="renewal_cost" 
                                           name="renewal_cost" 
                                           value="<?= e(old('renewal_cost')) ?>"
                                           step="0.01"
                                           min="0"
                                           placeholder="0.00">
                                    <?php if (has_error('renewal_cost')): ?>
                                        <div class="invalid-feedback">
                                            <?= e(error('renewal_cost')) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="auto_renewal" 
                                           name="auto_renewal" 
                                           value="1"
                                           <?= old('auto_renewal') ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="auto_renewal">
                                        Enable Auto Renewal
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="specifications" class="form-label">Server Specifications</label>
                        <textarea class="form-control <?= has_error('specifications') ? 'is-invalid' : '' ?>" 
                                  id="specifications" 
                                  name="specifications" 
                                  rows="3"
                                  placeholder="CPU: 2 cores, RAM: 4GB, Storage: 80GB SSD"><?= e(old('specifications')) ?></textarea>
                        <?php if (has_error('specifications')): ?>
                            <div class="invalid-feedback">
                                <?= e(error('specifications')) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control <?= has_error('notes') ? 'is-invalid' : '' ?>" 
                                  id="notes" 
                                  name="notes" 
                                  rows="4"
                                  placeholder="Additional notes about the server"><?= e(old('notes')) ?></textarea>
                        <?php if (has_error('notes')): ?>
                            <div class="invalid-feedback">
                                <?= e(error('notes')) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="<?= url('/servers') ?>" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            Add Server
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-lightbulb me-1"></i>
                    Tips
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-1"></i> Server Types</h6>
                    <ul class="mb-0 small">
                        <li><strong>Shared:</strong> Basic hosting plans</li>
                        <li><strong>VPS:</strong> Virtual private servers</li>
                        <li><strong>Dedicated:</strong> Physical servers</li>
                        <li><strong>Cloud:</strong> Cloud-based instances</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-1"></i> Renewal Alerts</h6>
                    <p class="mb-0 small">
                        The system will automatically send alerts before 
                        the server expires to prevent service interruption.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Auto-calculate expiry date (1 year from purchase)
    $('#purchase_date').on('change', function() {
        const purchaseDate = new Date($(this).val());
        if (purchaseDate && !$('#expiry_date').val()) {
            const expiryDate = new Date(purchaseDate);
            expiryDate.setFullYear(expiryDate.getFullYear() + 1);
            $('#expiry_date').val(expiryDate.toISOString().split('T')[0]);
        }
    });
    
    // IP address validation
    $('#ip_address').on('blur', function() {
        const ip = $(this).val().trim();
        if (ip) {
            const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
            if (!ipRegex.test(ip)) {
                $(this).addClass('is-invalid');
            } else {
                $(this).removeClass('is-invalid');
            }
        }
    });
    
    // Form validation
    $('#serverForm').on('submit', function(e) {
        let isValid = true;
        
        // Check required fields
        const requiredFields = ['client_id', 'server_name', 'server_type', 'purchase_date', 'expiry_date'];
        requiredFields.forEach(function(field) {
            const value = $(`#${field}`).val().trim();
            if (!value) {
                $(`#${field}`).addClass('is-invalid');
                isValid = false;
            } else {
                $(`#${field}`).removeClass('is-invalid');
            }
        });
        
        // Validate dates
        const purchaseDate = new Date($('#purchase_date').val());
        const expiryDate = new Date($('#expiry_date').val());
        
        if (expiryDate <= purchaseDate) {
            $('#expiry_date').addClass('is-invalid');
            isValid = false;
            ClientManager.showAlert('Expiry date must be after purchase date.', 'error');
        }
        
        if (!isValid) {
            e.preventDefault();
            ClientManager.showAlert('Please correct the errors below.', 'error');
        }
    });
});
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_PATH . '/views/layouts/app.php';
?>
