<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? e($title) . ' - ' : '' ?><?= e($config['app']['name']) ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="<?= asset('css/app.css') ?>" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= asset('favicon.ico') ?>">
    
    <meta name="csrf-token" content="<?= csrf_token() ?>">
</head>
<body class="<?= isset($bodyClass) ? e($bodyClass) : '' ?>">
    
    <?php if (isset($isLoggedIn) && $isLoggedIn): ?>
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand" href="<?= url('/dashboard') ?>">
                    <i class="fas fa-server me-2"></i>
                    <?= e($config['app']['name']) ?>
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link <?= active_menu('/dashboard') ?>" href="<?= url('/dashboard') ?>">
                                <i class="fas fa-tachometer-alt me-1"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= active_menu('/clients') ?>" href="<?= url('/clients') ?>">
                                <i class="fas fa-users me-1"></i> Clients
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= active_menu('/domains') ?>" href="<?= url('/domains') ?>">
                                <i class="fas fa-globe me-1"></i> Domains
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= active_menu('/servers') ?>" href="<?= url('/servers') ?>">
                                <i class="fas fa-server me-1"></i> Servers
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= active_menu('/mediators') ?>" href="<?= url('/mediators') ?>">
                                <i class="fas fa-handshake me-1"></i> Mediators
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= active_menu('/reports') ?>" href="<?= url('/reports') ?>">
                                <i class="fas fa-chart-bar me-1"></i> Reports
                            </a>
                        </li>
                        
                        <?php if (can('admin')): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-cog me-1"></i> Admin
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?= url('/users') ?>">
                                    <i class="fas fa-user-cog me-1"></i> Users
                                </a></li>
                                <li><a class="dropdown-item" href="<?= url('/settings') ?>">
                                    <i class="fas fa-cogs me-1"></i> Settings
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?= url('/backup') ?>">
                                    <i class="fas fa-download me-1"></i> Backup
                                </a></li>
                            </ul>
                        </li>
                        <?php endif; ?>
                    </ul>
                    
                    <ul class="navbar-nav">
                        <!-- Notifications -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle position-relative" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-bell"></i>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="notification-count">
                                    0
                                </span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                                <li><h6 class="dropdown-header">Notifications</h6></li>
                                <div id="notification-list">
                                    <li><span class="dropdown-item-text text-muted">No new notifications</span></li>
                                </div>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-center" href="<?= url('/notifications') ?>">View All</a></li>
                            </ul>
                        </li>
                        
                        <!-- User Menu -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <?= avatar($user, 24) ?>
                                <span class="ms-1"><?= e($user['first_name']) ?></span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><h6 class="dropdown-header">
                                    <?= e($user['first_name'] . ' ' . $user['last_name']) ?>
                                    <br><small class="text-muted"><?= e($user['role']) ?></small>
                                </h6></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?= url('/profile') ?>">
                                    <i class="fas fa-user me-1"></i> Profile
                                </a></li>
                                <li><a class="dropdown-item" href="<?= url('/settings') ?>">
                                    <i class="fas fa-cog me-1"></i> Settings
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?= url('/logout') ?>">
                                    <i class="fas fa-sign-out-alt me-1"></i> Logout
                                </a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    <?php endif; ?>
    
    <!-- Main Content -->
    <main class="<?= isset($isLoggedIn) && $isLoggedIn ? 'container-fluid mt-4' : '' ?>">
        <!-- Flash Messages -->
        <?php $flashMessages = flash_messages(); ?>
        <?php if (!empty($flashMessages)): ?>
            <div class="row">
                <div class="col-12">
                    <?php foreach ($flashMessages as $message): ?>
                        <div class="alert alert-<?= $message['type'] === 'error' ? 'danger' : $message['type'] ?> alert-dismissible fade show" role="alert">
                            <?= e($message['message']) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Page Content -->
        <?= $content ?>
    </main>
    
    <!-- Footer -->
    <?php if (isset($isLoggedIn) && $isLoggedIn): ?>
    <footer class="bg-light mt-5 py-3">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    <small class="text-muted">
                        &copy; <?= date('Y') ?> <?= e($config['app']['name']) ?>. All rights reserved.
                    </small>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted">
                        Version <?= e($config['app']['version']) ?> | 
                        <a href="#" class="text-decoration-none">Support</a>
                    </small>
                </div>
            </div>
        </div>
    </footer>
    <?php endif; ?>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?= asset('js/app.js') ?>"></script>
    
    <!-- Page-specific scripts -->
    <?php if (isset($scripts)): ?>
        <?php foreach ($scripts as $script): ?>
            <script src="<?= asset($script) ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <script>
        // Global JavaScript variables
        window.appConfig = {
            baseUrl: '<?= url() ?>',
            csrfToken: '<?= csrf_token() ?>',
            user: <?= json_encode($user ?? null) ?>
        };
        
        // Load notifications on page load
        $(document).ready(function() {
            loadNotifications();
            
            // Refresh notifications every 5 minutes
            setInterval(loadNotifications, 300000);
        });
        
        function loadNotifications() {
            $.get('<?= url('/api/notifications') ?>')
                .done(function(data) {
                    if (data.success) {
                        updateNotificationBadge(data.unread_count);
                        updateNotificationList(data.notifications);
                    }
                });
        }
        
        function updateNotificationBadge(count) {
            const badge = $('#notification-count');
            if (count > 0) {
                badge.text(count).show();
            } else {
                badge.hide();
            }
        }
        
        function updateNotificationList(notifications) {
            const list = $('#notification-list');
            list.empty();
            
            if (notifications.length === 0) {
                list.append('<li><span class="dropdown-item-text text-muted">No new notifications</span></li>');
            } else {
                notifications.slice(0, 5).forEach(function(notification) {
                    const item = `
                        <li>
                            <a class="dropdown-item" href="#" onclick="markAsRead(${notification.id})">
                                <div class="d-flex justify-content-between">
                                    <strong>${notification.title}</strong>
                                    <small class="text-muted">${notification.created_at}</small>
                                </div>
                                <div class="text-muted small">${notification.message}</div>
                            </a>
                        </li>
                    `;
                    list.append(item);
                });
            }
        }
        
        function markAsRead(notificationId) {
            $.post('<?= url('/api/notifications/mark-read') ?>', {
                id: notificationId,
                _token: '<?= csrf_token() ?>'
            }).done(function() {
                loadNotifications();
            });
        }
    </script>
</body>
</html>
