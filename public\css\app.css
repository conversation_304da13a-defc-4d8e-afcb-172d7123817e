/* Custom CSS for Client Manager System */

:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Navigation */
.navbar-brand {
    font-weight: 600;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 5px;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: all 0.3s;
}

.card:hover {
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

/* Buttons */
.btn {
    border-radius: 0.35rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(45deg, var(--success-color), #1e7e34);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(45deg, #1e7e34, #155724);
    transform: translateY(-1px);
}

/* Tables */
.table {
    background-color: white;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #5a5c69;
    background-color: #f8f9fc;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Forms */
.form-control {
    border-radius: 0.35rem;
    border: 1px solid #d1d3e2;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-select {
    border-radius: 0.35rem;
    border: 1px solid #d1d3e2;
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Alerts */
.alert {
    border: none;
    border-radius: 0.35rem;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(45deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(45deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(45deg, #fff3cd, #ffeaa7);
    color: #856404;
}

.alert-info {
    background: linear-gradient(45deg, #d1ecf1, #bee5eb);
    color: #0c5460;
}

/* Badges */
.badge {
    font-weight: 500;
    border-radius: 0.25rem;
}

.badge-success {
    background-color: var(--success-color);
}

.badge-danger {
    background-color: var(--danger-color);
}

.badge-warning {
    background-color: var(--warning-color);
    color: #212529;
}

.badge-info {
    background-color: var(--info-color);
}

.badge-secondary {
    background-color: var(--secondary-color);
}

.badge-dark {
    background-color: var(--dark-color);
}

/* Avatar */
.avatar {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--primary-color), #0056b3);
    color: white;
}

/* Status indicators */
.status-active {
    color: var(--success-color);
}

.status-expired {
    color: var(--danger-color);
}

.status-suspended {
    color: var(--warning-color);
}

.status-terminated {
    color: var(--dark-color);
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn {
        font-size: 0.875rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1a1a1a;
        --text-color: #ffffff;
        --card-bg: #2d2d2d;
    }
    
    body.dark-mode {
        background-color: var(--bg-color);
        color: var(--text-color);
    }
    
    .dark-mode .card {
        background-color: var(--card-bg);
        color: var(--text-color);
    }
    
    .dark-mode .table {
        background-color: var(--card-bg);
        color: var(--text-color);
    }
    
    .dark-mode .form-control {
        background-color: var(--card-bg);
        border-color: #495057;
        color: var(--text-color);
    }
}

/* Utility classes */
.text-xs {
    font-size: 0.75rem;
}

.text-sm {
    font-size: 0.875rem;
}

.font-weight-bold {
    font-weight: 700;
}

.border-left-primary {
    border-left: 0.25rem solid var(--primary-color);
}

.border-left-success {
    border-left: 0.25rem solid var(--success-color);
}

.border-left-info {
    border-left: 0.25rem solid var(--info-color);
}

.border-left-warning {
    border-left: 0.25rem solid var(--warning-color);
}

.border-left-danger {
    border-left: 0.25rem solid var(--danger-color);
}

/* Animation classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateY(-10px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Print styles */
@media print {
    .navbar,
    .btn,
    .alert,
    .card-header {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
}
