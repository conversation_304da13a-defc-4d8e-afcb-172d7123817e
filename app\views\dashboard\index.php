<?php
$content = ob_get_clean();
ob_start();
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-tachometer-alt me-2"></i>
                Dashboard
            </h1>
            <div>
                <span class="text-muted">Welcome back, <?= e($user['first_name']) ?>!</span>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Clients
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= number_format($data['stats']['clients']['total_clients']) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Active Domains
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= number_format($data['stats']['domains']['active_domains']) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-globe fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Active Servers
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= number_format($data['stats']['servers']['active_servers']) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-server fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Total Revenue
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= format_currency($data['revenue_summary']['total_revenue']) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Alerts Row -->
<div class="row mb-4">
    <!-- Expiring Domains -->
    <?php if (!empty($data['expiring_domains'])): ?>
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Expiring Domains (30 days)
                </h6>
                <a href="<?= url('/domains?filter=expiring') ?>" class="btn btn-sm btn-outline-warning">
                    View All
                </a>
            </div>
            <div class="card-body">
                <?php foreach (array_slice($data['expiring_domains'], 0, 5) as $domain): ?>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div>
                        <strong><?= e($domain['domain_name']) ?></strong>
                        <br>
                        <small class="text-muted"><?= e($domain['client_name']) ?></small>
                    </div>
                    <div class="text-end">
                        <?= expiry_alert($domain['expiry_date']) ?>
                        <br>
                        <small class="text-muted"><?= format_date($domain['expiry_date']) ?></small>
                    </div>
                </div>
                <?php if (!$loop->last): ?><hr><?php endif; ?>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- Expiring Servers -->
    <?php if (!empty($data['expiring_servers'])): ?>
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Expiring Servers (30 days)
                </h6>
                <a href="<?= url('/servers?filter=expiring') ?>" class="btn btn-sm btn-outline-warning">
                    View All
                </a>
            </div>
            <div class="card-body">
                <?php foreach (array_slice($data['expiring_servers'], 0, 5) as $server): ?>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div>
                        <strong><?= e($server['server_name']) ?></strong>
                        <br>
                        <small class="text-muted"><?= e($server['client_name']) ?></small>
                    </div>
                    <div class="text-end">
                        <?= expiry_alert($server['expiry_date']) ?>
                        <br>
                        <small class="text-muted"><?= format_date($server['expiry_date']) ?></small>
                    </div>
                </div>
                <?php if (!$loop->last): ?><hr><?php endif; ?>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Content Row -->
<div class="row">
    <!-- Upcoming Renewals -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-calendar-alt me-2"></i>
                    Upcoming Renewals (7 days)
                </h6>
            </div>
            <div class="card-body">
                <?php if (empty($data['upcoming_renewals'])): ?>
                    <p class="text-muted text-center">No renewals in the next 7 days</p>
                <?php else: ?>
                    <?php foreach ($data['upcoming_renewals'] as $renewal): ?>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-<?= $renewal['type'] === 'domain' ? 'globe' : 'server' ?> me-2 text-<?= $renewal['type'] === 'domain' ? 'success' : 'info' ?>"></i>
                                <div>
                                    <strong><?= e($renewal['name']) ?></strong>
                                    <br>
                                    <small class="text-muted"><?= e($renewal['client_name']) ?></small>
                                </div>
                            </div>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-warning text-dark">
                                <?= days_until($renewal['expiry_date']) ?> days
                            </span>
                            <br>
                            <small class="text-muted"><?= format_currency($renewal['renewal_cost']) ?></small>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Top Clients -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-star me-2"></i>
                    Top Clients by Value
                </h6>
            </div>
            <div class="card-body">
                <?php if (empty($data['top_clients'])): ?>
                    <p class="text-muted text-center">No client data available</p>
                <?php else: ?>
                    <?php foreach ($data['top_clients'] as $client): ?>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <strong><?= e($client['name']) ?></strong>
                            <br>
                            <small class="text-muted">
                                <?= $client['domain_count'] ?> domains, <?= $client['server_count'] ?> servers
                            </small>
                        </div>
                        <div class="text-end">
                            <strong class="text-success"><?= format_currency($client['total_value']) ?></strong>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-history me-2"></i>
                    Recent Activities
                </h6>
            </div>
            <div class="card-body">
                <?php if (empty($data['recent_activities'])): ?>
                    <p class="text-muted text-center">No recent activities</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Action</th>
                                    <th>Table</th>
                                    <th>Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($data['recent_activities'], 0, 10) as $activity): ?>
                                <tr>
                                    <td>
                                        <?= e($activity['first_name'] . ' ' . $activity['last_name']) ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary"><?= e($activity['action']) ?></span>
                                    </td>
                                    <td><?= e($activity['table_name']) ?></td>
                                    <td>
                                        <small class="text-muted">
                                            <?= format_date($activity['created_at'], 'M j, Y g:i A') ?>
                                        </small>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }
    
    .border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }
    
    .border-left-info {
        border-left: 0.25rem solid #36b9cc !important;
    }
    
    .border-left-warning {
        border-left: 0.25rem solid #f6c23e !important;
    }
    
    .text-xs {
        font-size: 0.7rem;
    }
    
    .text-gray-300 {
        color: #dddfeb !important;
    }
    
    .text-gray-800 {
        color: #5a5c69 !important;
    }
    
    .font-weight-bold {
        font-weight: 700 !important;
    }
</style>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_PATH . '/views/layouts/app.php';
?>
