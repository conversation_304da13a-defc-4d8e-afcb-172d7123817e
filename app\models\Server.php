<?php
/**
 * Server Model
 */

class Server extends Model
{
    protected $table = 'servers';
    protected $fillable = [
        'client_id', 'mediator_id', 'server_name', 'provider', 'server_type', 
        'ip_address', 'purchase_date', 'expiry_date', 'renewal_cost', 
        'auto_renewal', 'specifications', 'status', 'notes'
    ];
    
    /**
     * Get server with client and mediator info
     */
    public function getServerWithRelations($id)
    {
        $sql = "SELECT s.*, c.name as client_name, c.email as client_email,
                       m.name as mediator_name, m.email as mediator_email
                FROM servers s
                LEFT JOIN clients c ON s.client_id = c.id
                LEFT JOIN mediators m ON s.mediator_id = m.id
                WHERE s.id = ?";
        
        $result = $this->query($sql, [$id]);
        return $result[0] ?? null;
    }
    
    /**
     * Get servers with client info
     */
    public function getServersWithClients($conditions = [], $orderBy = 'expiry_date ASC')
    {
        $sql = "SELECT s.*, c.name as client_name, c.email as client_email
                FROM servers s
                LEFT JOIN clients c ON s.client_id = c.id";
        
        $params = [];
        if (!empty($conditions)) {
            $whereClause = [];
            foreach ($conditions as $field => $value) {
                $whereClause[] = "s.{$field} = ?";
                $params[] = $value;
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }
        
        if ($orderBy) {
            $sql .= " ORDER BY s.{$orderBy}";
        }
        
        return $this->query($sql, $params);
    }
    
    /**
     * Get expiring servers
     */
    public function getExpiringServers($days = 30)
    {
        $date = date('Y-m-d', strtotime("+{$days} days"));
        
        $sql = "SELECT s.*, c.name as client_name, c.email as client_email,
                       DATEDIFF(s.expiry_date, CURDATE()) as days_until_expiry
                FROM servers s
                LEFT JOIN clients c ON s.client_id = c.id
                WHERE s.expiry_date <= ? AND s.status = 'active'
                ORDER BY s.expiry_date ASC";
        
        return $this->query($sql, [$date]);
    }
    
    /**
     * Get expired servers
     */
    public function getExpiredServers()
    {
        $sql = "SELECT s.*, c.name as client_name, c.email as client_email,
                       DATEDIFF(CURDATE(), s.expiry_date) as days_expired
                FROM servers s
                LEFT JOIN clients c ON s.client_id = c.id
                WHERE s.expiry_date < CURDATE() AND s.status = 'active'
                ORDER BY s.expiry_date ASC";
        
        return $this->query($sql);
    }
    
    /**
     * Search servers
     */
    public function searchServers($query)
    {
        $sql = "SELECT s.*, c.name as client_name
                FROM servers s
                LEFT JOIN clients c ON s.client_id = c.id
                WHERE s.server_name LIKE ? OR s.provider LIKE ? OR s.ip_address LIKE ? OR c.name LIKE ?
                ORDER BY s.server_name ASC";
        
        $searchTerm = "%{$query}%";
        return $this->query($sql, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
    }
    
    /**
     * Get server statistics
     */
    public function getStats()
    {
        $stats = [
            'total_servers' => $this->count(),
            'active_servers' => $this->count(['status' => 'active']),
            'expired_servers' => $this->count(['status' => 'expired']),
            'suspended_servers' => $this->count(['status' => 'suspended'])
        ];
        
        // Get expiring servers count
        $expiringCount = $this->query(
            "SELECT COUNT(*) as count FROM servers 
             WHERE expiry_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) 
             AND status = 'active'"
        );
        $stats['expiring_30_days'] = $expiringCount[0]['count'] ?? 0;
        
        // Get total renewal cost
        $totalCost = $this->query(
            "SELECT SUM(renewal_cost) as total FROM servers WHERE status = 'active'"
        );
        $stats['total_renewal_cost'] = $totalCost[0]['total'] ?? 0;
        
        // Get servers by type
        $serverTypes = $this->query(
            "SELECT server_type, COUNT(*) as count FROM servers 
             WHERE status = 'active' 
             GROUP BY server_type"
        );
        $stats['by_type'] = [];
        foreach ($serverTypes as $type) {
            $stats['by_type'][$type['server_type']] = $type['count'];
        }
        
        return $stats;
    }
    
    /**
     * Get servers by provider
     */
    public function getServersByProvider()
    {
        $sql = "SELECT provider, COUNT(*) as count, SUM(renewal_cost) as total_cost
                FROM servers 
                WHERE status = 'active' AND provider IS NOT NULL
                GROUP BY provider
                ORDER BY count DESC";
        
        return $this->query($sql);
    }
    
    /**
     * Update server status based on expiry
     */
    public function updateExpiredServers()
    {
        $sql = "UPDATE servers SET status = 'expired' 
                WHERE expiry_date < CURDATE() AND status = 'active'";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute();
    }
    
    /**
     * Get renewal calendar
     */
    public function getRenewalCalendar($year = null, $month = null)
    {
        $year = $year ?: date('Y');
        $month = $month ?: date('m');
        
        $sql = "SELECT s.*, c.name as client_name
                FROM servers s
                LEFT JOIN clients c ON s.client_id = c.id
                WHERE YEAR(s.expiry_date) = ? AND MONTH(s.expiry_date) = ?
                AND s.status = 'active'
                ORDER BY s.expiry_date ASC";
        
        return $this->query($sql, [$year, $month]);
    }
    
    /**
     * Get server types
     */
    public function getServerTypes()
    {
        return ['shared', 'vps', 'dedicated', 'cloud'];
    }
}
