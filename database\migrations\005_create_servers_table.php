<?php
$sql = "CREATE TABLE IF NOT EXISTS servers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_id INT NOT NULL,
    mediator_id INT,
    server_name VARCHAR(100) NOT NULL,
    provider VARCHAR(100),
    server_type ENUM('shared', 'vps', 'dedicated', 'cloud') DEFAULT 'shared',
    ip_address VARCHAR(45),
    purchase_date DATE NOT NULL,
    expiry_date DATE NOT NULL,
    renewal_cost DECIMAL(10,2),
    auto_renewal BOOLEAN DEFAULT FALSE,
    specifications TEXT,
    status ENUM('active', 'expired', 'suspended', 'terminated') DEFAULT 'active',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOR<PERSON><PERSON><PERSON> KEY (mediator_id) REFERENCES mediators(id) ON DELETE SET NULL
)";

$this->connection->exec($sql);
