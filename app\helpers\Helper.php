<?php
/**
 * Helper Functions
 * Global utility functions
 */

class Helper
{
    /**
     * Sanitize input data
     */
    public static function sanitize($data)
    {
        if (is_array($data)) {
            return array_map([self::class, 'sanitize'], $data);
        }
        
        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Generate random string
     */
    public static function randomString($length = 10)
    {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        return substr(str_shuffle($chars), 0, $length);
    }
    
    /**
     * Format file size
     */
    public static function formatFileSize($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * Send email
     */
    public static function sendEmail($to, $subject, $message, $headers = [])
    {
        $config = include CONFIG_PATH . '/config.php';
        $mailConfig = $config['mail'];
        
        // Basic email headers
        $defaultHeaders = [
            'From' => $mailConfig['from_name'] . ' <' . $mailConfig['from_address'] . '>',
            'Reply-To' => $mailConfig['from_address'],
            'Content-Type' => 'text/html; charset=UTF-8',
            'X-Mailer' => 'Client Manager System'
        ];
        
        $headers = array_merge($defaultHeaders, $headers);
        
        // Convert headers array to string
        $headerString = '';
        foreach ($headers as $key => $value) {
            $headerString .= $key . ': ' . $value . "\r\n";
        }
        
        // Send email
        return mail($to, $subject, $message, $headerString);
    }
    
    /**
     * Log message
     */
    public static function log($message, $level = 'info', $context = [])
    {
        $logFile = ROOT_PATH . '/logs/' . date('Y-m-d') . '.log';
        
        // Create logs directory if it doesn't exist
        $logDir = dirname($logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $timestamp = date('Y-m-d H:i:s');
        $contextString = !empty($context) ? ' ' . json_encode($context) : '';
        $logEntry = "[{$timestamp}] {$level}: {$message}{$contextString}" . PHP_EOL;
        
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Validate domain name
     */
    public static function isValidDomain($domain)
    {
        return filter_var($domain, FILTER_VALIDATE_DOMAIN, FILTER_FLAG_HOSTNAME);
    }
    
    /**
     * Validate IP address
     */
    public static function isValidIP($ip)
    {
        return filter_var($ip, FILTER_VALIDATE_IP);
    }
    
    /**
     * Generate slug from string
     */
    public static function slug($string)
    {
        $string = strtolower($string);
        $string = preg_replace('/[^a-z0-9\s-]/', '', $string);
        $string = preg_replace('/[\s-]+/', '-', $string);
        return trim($string, '-');
    }
    
    /**
     * Check if date is in the past
     */
    public static function isPastDate($date)
    {
        return strtotime($date) < time();
    }
    
    /**
     * Get days between dates
     */
    public static function daysBetween($date1, $date2)
    {
        $datetime1 = new DateTime($date1);
        $datetime2 = new DateTime($date2);
        $interval = $datetime1->diff($datetime2);
        
        return $interval->days;
    }
    
    /**
     * Format phone number
     */
    public static function formatPhone($phone)
    {
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        if (strlen($phone) == 10) {
            return preg_replace('/(\d{3})(\d{3})(\d{4})/', '($1) $2-$3', $phone);
        }
        
        return $phone;
    }
    
    /**
     * Generate QR code URL
     */
    public static function qrCode($data, $size = 200)
    {
        return "https://api.qrserver.com/v1/create-qr-code/?size={$size}x{$size}&data=" . urlencode($data);
    }
    
    /**
     * Backup database
     */
    public static function backupDatabase()
    {
        $config = include CONFIG_PATH . '/database.php';
        $dbConfig = $config['connections'][$config['default']];
        
        if ($dbConfig['driver'] !== 'mysql') {
            return false;
        }
        
        $backupDir = ROOT_PATH . '/backups';
        if (!is_dir($backupDir)) {
            mkdir($backupDir, 0755, true);
        }
        
        $filename = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
        $filepath = $backupDir . '/' . $filename;
        
        $command = sprintf(
            'mysqldump --host=%s --user=%s --password=%s %s > %s',
            escapeshellarg($dbConfig['host']),
            escapeshellarg($dbConfig['username']),
            escapeshellarg($dbConfig['password']),
            escapeshellarg($dbConfig['database']),
            escapeshellarg($filepath)
        );
        
        exec($command, $output, $returnCode);
        
        return $returnCode === 0 ? $filename : false;
    }
    
    /**
     * Clean old backups
     */
    public static function cleanOldBackups($days = 30)
    {
        $backupDir = ROOT_PATH . '/backups';
        
        if (!is_dir($backupDir)) {
            return 0;
        }
        
        $files = glob($backupDir . '/backup_*.sql');
        $cutoff = time() - ($days * 24 * 60 * 60);
        $deleted = 0;
        
        foreach ($files as $file) {
            if (filemtime($file) < $cutoff) {
                unlink($file);
                $deleted++;
            }
        }
        
        return $deleted;
    }
    
    /**
     * Get system info
     */
    public static function getSystemInfo()
    {
        return [
            'php_version' => PHP_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
            'server_name' => $_SERVER['SERVER_NAME'] ?? 'Unknown',
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
            'timezone' => date_default_timezone_get(),
            'disk_free_space' => self::formatFileSize(disk_free_space('.')),
            'disk_total_space' => self::formatFileSize(disk_total_space('.'))
        ];
    }
    
    /**
     * Check for upcoming expirations
     */
    public static function checkExpirations()
    {
        $db = Database::getInstance()->getConnection();
        $config = include CONFIG_PATH . '/config.php';
        $alertDays = $config['notifications']['domain_expiry_alert_days'];
        
        $notifications = [];
        
        // Check domains
        foreach ($alertDays as $days) {
            $date = date('Y-m-d', strtotime("+{$days} days"));
            
            $stmt = $db->prepare("
                SELECT d.*, c.name as client_name 
                FROM domains d 
                JOIN clients c ON d.client_id = c.id 
                WHERE d.expiry_date = ? AND d.status = 'active'
            ");
            $stmt->execute([$date]);
            $domains = $stmt->fetchAll();
            
            foreach ($domains as $domain) {
                $notifications[] = [
                    'type' => 'domain_expiry',
                    'title' => "Domain Expiring in {$days} days",
                    'message' => "Domain {$domain['domain_name']} for client {$domain['client_name']} expires on {$domain['expiry_date']}",
                    'related_id' => $domain['id'],
                    'related_type' => 'domain'
                ];
            }
        }
        
        // Check servers
        foreach ($alertDays as $days) {
            $date = date('Y-m-d', strtotime("+{$days} days"));
            
            $stmt = $db->prepare("
                SELECT s.*, c.name as client_name 
                FROM servers s 
                JOIN clients c ON s.client_id = c.id 
                WHERE s.expiry_date = ? AND s.status = 'active'
            ");
            $stmt->execute([$date]);
            $servers = $stmt->fetchAll();
            
            foreach ($servers as $server) {
                $notifications[] = [
                    'type' => 'server_expiry',
                    'title' => "Server Expiring in {$days} days",
                    'message' => "Server {$server['server_name']} for client {$server['client_name']} expires on {$server['expiry_date']}",
                    'related_id' => $server['id'],
                    'related_type' => 'server'
                ];
            }
        }
        
        // Save notifications
        foreach ($notifications as $notification) {
            $stmt = $db->prepare("
                INSERT INTO notifications (type, title, message, related_id, related_type, created_at) 
                VALUES (?, ?, ?, ?, ?, NOW())
            ");
            $stmt->execute([
                $notification['type'],
                $notification['title'],
                $notification['message'],
                $notification['related_id'],
                $notification['related_type']
            ]);
        }
        
        return count($notifications);
    }
}
